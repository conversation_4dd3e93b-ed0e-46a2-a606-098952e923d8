/**
 * Semantic HTML and ARIA Validator
 * Enhanced semantic validation with better landmark detection, heading structure analysis, and ARIA usage patterns
 */

import { Page } from 'puppeteer';
import logger from '../../../utils/logger';

export interface LandmarkAnalysis {
  type: string;
  selector: string;
  hasLabel: boolean;
  label?: string;
  isUnique: boolean;
  isProperlyNested: boolean;
  issues: string[];
  recommendations: string[];
}

export interface HeadingStructure {
  level: number;
  text: string;
  selector: string;
  hasProperHierarchy: boolean;
  isSkipped: boolean;
  isEmpty: boolean;
  issues: string[];
}

export interface AriaPattern {
  pattern: string;
  element: string;
  selector: string;
  isValid: boolean;
  requiredAttributes: string[];
  missingAttributes: string[];
  invalidValues: string[];
  issues: string[];
  recommendations: string[];
}

export interface SemanticValidationReport {
  landmarks: {
    total: number;
    valid: number;
    missing: string[];
    duplicates: string[];
    analysis: LandmarkAnalysis[];
  };
  headings: {
    total: number;
    structure: HeadingStructure[];
    hasH1: boolean;
    hasProperHierarchy: boolean;
    skippedLevels: number[];
    emptyHeadings: number;
  };
  aria: {
    total: number;
    valid: number;
    patterns: AriaPattern[];
    commonIssues: string[];
    invalidRoles: string[];
    missingLabels: string[];
  };
  semanticElements: {
    total: number;
    used: string[];
    missing: string[];
    misused: string[];
  };
  overallScore: number;
  criticalIssues: string[];
  recommendations: string[];
}

export interface SemanticValidationConfig {
  validateLandmarks: boolean;
  validateHeadings: boolean;
  validateAria: boolean;
  validateSemanticElements: boolean;
  strictMode: boolean;
  includeHiddenElements: boolean;
}

/**
 * Enhanced semantic HTML and ARIA validator
 */
export class SemanticValidator {
  private static instance: SemanticValidator;

  private constructor() {}

  static getInstance(): SemanticValidator {
    if (!SemanticValidator.instance) {
      SemanticValidator.instance = new SemanticValidator();
    }
    return SemanticValidator.instance;
  }

  /**
   * Validate semantic HTML and ARIA patterns
   */
  async validateSemantics(
    page: Page,
    config: Partial<SemanticValidationConfig> = {},
  ): Promise<SemanticValidationReport> {
    const fullConfig: SemanticValidationConfig = {
      validateLandmarks: config.validateLandmarks ?? true,
      validateHeadings: config.validateHeadings ?? true,
      validateAria: config.validateAria ?? true,
      validateSemanticElements: config.validateSemanticElements ?? true,
      strictMode: config.strictMode ?? false,
      includeHiddenElements: config.includeHiddenElements ?? false,
    };

    logger.debug('🏗️ Starting semantic HTML and ARIA validation');

    // Inject validation functions
    await this.injectValidationFunctions(page);

    // Perform validations
    const [landmarks, headings, aria, semanticElements] = await Promise.all([
      fullConfig.validateLandmarks
        ? this.validateLandmarks(page, fullConfig)
        : this.getEmptyLandmarkAnalysis(),
      fullConfig.validateHeadings
        ? this.validateHeadings(page, fullConfig)
        : this.getEmptyHeadingAnalysis(),
      fullConfig.validateAria ? this.validateAria(page, fullConfig) : this.getEmptyAriaAnalysis(),
      fullConfig.validateSemanticElements
        ? this.validateSemanticElements(page, fullConfig)
        : this.getEmptySemanticAnalysis(),
    ]);

    // Generate comprehensive report
    const report = this.generateReport(landmarks, headings, aria, semanticElements);

    logger.info(`✅ Semantic validation completed`, {
      landmarksValid: `${landmarks.valid}/${landmarks.total}`,
      headingsValid: headings.hasProperHierarchy,
      ariaValid: `${aria.valid}/${aria.total}`,
      overallScore: report.overallScore,
    });

    return report;
  }

  /**
   * Inject validation functions into the page
   */
  private async injectValidationFunctions(page: Page): Promise<void> {
    await page.evaluateOnNewDocument(() => {
      (window as any).semanticValidation = {
        /**
         * Get element selector
         */
        getElementSelector(element: HTMLElement): string {
          if (element.id) return `#${element.id}`;

          const path = [];
          let current = element;

          while (current && current.nodeType === Node.ELEMENT_NODE && current !== document.body) {
            let selector = current.nodeName.toLowerCase();

            if (current.className) {
              const classes = current.className.split(' ').filter((c) => c.trim());
              if (classes.length > 0) {
                selector += '.' + classes.slice(0, 2).join('.');
              }
            }

            path.unshift(selector);
            current = current.parentElement!;

            if (path.length > 4) break;
          }

          return path.join(' > ');
        },

        /**
         * Check if element is visible
         */
        isVisible(element: HTMLElement): boolean {
          const style = window.getComputedStyle(element);
          return style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0';
        },

        /**
         * Validate landmarks
         */
        validateLandmarks(includeHidden: boolean): unknown {
          const landmarkSelectors = [
            { selector: 'main, [role="main"]', type: 'main' },
            { selector: 'nav, [role="navigation"]', type: 'navigation' },
            { selector: 'header, [role="banner"]', type: 'banner' },
            { selector: 'footer, [role="contentinfo"]', type: 'contentinfo' },
            { selector: 'aside, [role="complementary"]', type: 'complementary' },
            { selector: '[role="search"]', type: 'search' },
            { selector: 'section, [role="region"]', type: 'region' },
            { selector: 'form, [role="form"]', type: 'form' },
          ];

          const analysis: unknown[] = [];
          const foundTypes: string[] = [];

          landmarkSelectors.forEach(({ selector, type }) => {
            const elements = document.querySelectorAll(selector);

            elements.forEach((el) => {
              const element = el as HTMLElement;

              if (!includeHidden && !this.isVisible(element)) {
                return;
              }

              const hasLabel = !!(
                element.getAttribute('aria-label') ||
                element.getAttribute('aria-labelledby') ||
                element.getAttribute('title')
              );

              const label =
                element.getAttribute('aria-label') ||
                (element.getAttribute('aria-labelledby') &&
                  document.getElementById(element.getAttribute('aria-labelledby')!)?.textContent) ||
                element.getAttribute('title');

              const isUnique = foundTypes.filter((t) => t === type).length === 0;
              foundTypes.push(type);

              const issues: string[] = [];
              const recommendations: string[] = [];

              // Check for multiple main landmarks
              if (type === 'main' && foundTypes.filter((t) => t === 'main').length > 1) {
                issues.push('Multiple main landmarks found');
                recommendations.push('Use only one main landmark per page');
              }

              // Check for missing labels on navigation
              if (
                type === 'navigation' &&
                !hasLabel &&
                foundTypes.filter((t) => t === 'navigation').length > 1
              ) {
                issues.push('Navigation landmark missing label');
                recommendations.push('Add aria-label to distinguish multiple navigation landmarks');
              }

              // Check for proper nesting
              const isProperlyNested = this.checkLandmarkNesting(element, type);
              if (!isProperlyNested) {
                issues.push('Landmark improperly nested');
                recommendations.push('Review landmark hierarchy and nesting');
              }

              analysis.push({
                type,
                selector: this.getElementSelector(element),
                hasLabel,
                label,
                isUnique,
                isProperlyNested,
                issues,
                recommendations,
              });
            });
          });

          // Check for missing required landmarks
          const missing: string[] = [];
          if (!foundTypes.includes('main')) missing.push('main');
          if (!foundTypes.includes('banner')) missing.push('banner');
          if (!foundTypes.includes('contentinfo')) missing.push('contentinfo');

          // Check for duplicates
          const duplicates: string[] = [];
          const typeCounts: { [key: string]: number } = {};
          foundTypes.forEach((type) => {
            typeCounts[type] = (typeCounts[type] || 0) + 1;
            if (typeCounts[type] > 1 && !duplicates.includes(type)) {
              duplicates.push(type);
            }
          });

          return {
            total: analysis.length,
            valid: analysis.filter((a) => a.issues.length === 0).length,
            missing,
            duplicates,
            analysis,
          };
        },

        /**
         * Check landmark nesting
         */
        checkLandmarkNesting(element: HTMLElement, type: string): boolean {
          // Main should not be nested in other landmarks
          if (type === 'main') {
            let parent = element.parentElement;
            while (parent) {
              const parentRole = parent.getAttribute('role') || parent.tagName.toLowerCase();
              if (['banner', 'contentinfo', 'navigation', 'complementary'].includes(parentRole)) {
                return false;
              }
              parent = parent.parentElement;
            }
          }

          return true;
        },

        /**
         * Validate heading structure
         */
        validateHeadings(includeHidden: boolean): unknown {
          const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
          const structure: unknown[] = [];
          let previousLevel = 0;
          let hasH1 = false;
          const skippedLevels: number[] = [];
          let emptyHeadings = 0;

          headings.forEach((heading) => {
            const element = heading as HTMLElement;

            if (!includeHidden && !this.isVisible(element)) {
              return;
            }

            const level = parseInt(element.tagName.charAt(1));
            const text = element.textContent?.trim() || '';
            const isEmpty = text === '';

            if (isEmpty) emptyHeadings++;
            if (level === 1) hasH1 = true;

            const issues: string[] = [];

            // Check for skipped levels
            const isSkipped = previousLevel > 0 && level > previousLevel + 1;
            if (isSkipped) {
              const skipped = level - previousLevel - 1;
              for (let i = 1; i <= skipped; i++) {
                const skippedLevel = previousLevel + i;
                if (!skippedLevels.includes(skippedLevel)) {
                  skippedLevels.push(skippedLevel);
                }
              }
              issues.push(`Heading level skipped (h${previousLevel} to h${level})`);
            }

            if (isEmpty) {
              issues.push('Heading is empty');
            }

            structure.push({
              level,
              text,
              selector: this.getElementSelector(element),
              hasProperHierarchy: !isSkipped,
              isSkipped,
              isEmpty,
              issues,
            });

            previousLevel = level;
          });

          return {
            total: structure.length,
            structure,
            hasH1,
            hasProperHierarchy: skippedLevels.length === 0,
            skippedLevels,
            emptyHeadings,
          };
        },

        /**
         * Validate ARIA patterns
         */
        validateAria(includeHidden: boolean): unknown {
          const ariaPatterns: unknown[] = [];
          const commonIssues: string[] = [];
          const invalidRoles: string[] = [];
          const missingLabels: string[] = [];

          // Define ARIA patterns to validate
          const patterns = [
            {
              pattern: 'button',
              selector: '[role="button"], button',
              requiredAttributes: [],
              optionalAttributes: ['aria-label', 'aria-labelledby', 'aria-describedby'],
            },
            {
              pattern: 'checkbox',
              selector: '[role="checkbox"], input[type="checkbox"]',
              requiredAttributes: ['aria-checked'],
              optionalAttributes: ['aria-label', 'aria-labelledby'],
            },
            {
              pattern: 'tab',
              selector: '[role="tab"]',
              requiredAttributes: ['aria-selected'],
              optionalAttributes: ['aria-label', 'aria-labelledby', 'aria-controls'],
            },
            {
              pattern: 'tabpanel',
              selector: '[role="tabpanel"]',
              requiredAttributes: [],
              optionalAttributes: ['aria-label', 'aria-labelledby'],
            },
            {
              pattern: 'menuitem',
              selector: '[role="menuitem"]',
              requiredAttributes: [],
              optionalAttributes: ['aria-label', 'aria-labelledby'],
            },
            {
              pattern: 'dialog',
              selector: '[role="dialog"], dialog',
              requiredAttributes: ['aria-label', 'aria-labelledby'],
              optionalAttributes: ['aria-describedby'],
            },
          ];

          patterns.forEach(({ pattern, selector, requiredAttributes, optionalAttributes }) => {
            const elements = document.querySelectorAll(selector);

            elements.forEach((el) => {
              const element = el as HTMLElement;

              if (!includeHidden && !this.isVisible(element)) {
                return;
              }

              const issues: string[] = [];
              const recommendations: string[] = [];
              const missingAttributes: string[] = [];
              const invalidValues: string[] = [];

              // Check required attributes
              requiredAttributes.forEach((attr) => {
                if (!element.hasAttribute(attr)) {
                  missingAttributes.push(attr);
                  issues.push(`Missing required attribute: ${attr}`);
                }
              });

              // Check for proper labeling
              const hasLabel = !!(
                element.getAttribute('aria-label') ||
                element.getAttribute('aria-labelledby') ||
                element.textContent?.trim()
              );

              if (!hasLabel && ['button', 'checkbox', 'tab', 'menuitem'].includes(pattern)) {
                missingLabels.push(this.getElementSelector(element));
                issues.push('Element lacks accessible label');
                recommendations.push('Add aria-label or ensure element has text content');
              }

              // Validate specific patterns
              if (pattern === 'checkbox') {
                const ariaChecked = element.getAttribute('aria-checked');
                if (ariaChecked && !['true', 'false', 'mixed'].includes(ariaChecked)) {
                  invalidValues.push(`aria-checked="${ariaChecked}"`);
                  issues.push('Invalid aria-checked value');
                }
              }

              if (pattern === 'tab') {
                const ariaSelected = element.getAttribute('aria-selected');
                if (ariaSelected && !['true', 'false'].includes(ariaSelected)) {
                  invalidValues.push(`aria-selected="${ariaSelected}"`);
                  issues.push('Invalid aria-selected value');
                }
              }

              ariaPatterns.push({
                pattern,
                element: element.tagName.toLowerCase(),
                selector: this.getElementSelector(element),
                isValid: issues.length === 0,
                requiredAttributes,
                missingAttributes,
                invalidValues,
                issues,
                recommendations,
              });
            });
          });

          // Check for invalid roles
          const elementsWithRoles = document.querySelectorAll('[role]');
          const validRoles = [
            'alert',
            'alertdialog',
            'application',
            'article',
            'banner',
            'button',
            'cell',
            'checkbox',
            'columnheader',
            'combobox',
            'complementary',
            'contentinfo',
            'definition',
            'dialog',
            'directory',
            'document',
            'feed',
            'figure',
            'form',
            'grid',
            'gridcell',
            'group',
            'heading',
            'img',
            'link',
            'list',
            'listbox',
            'listitem',
            'log',
            'main',
            'marquee',
            'math',
            'menu',
            'menubar',
            'menuitem',
            'menuitemcheckbox',
            'menuitemradio',
            'navigation',
            'none',
            'note',
            'option',
            'presentation',
            'progressbar',
            'radio',
            'radiogroup',
            'region',
            'row',
            'rowgroup',
            'rowheader',
            'scrollbar',
            'search',
            'searchbox',
            'separator',
            'slider',
            'spinbutton',
            'status',
            'switch',
            'tab',
            'table',
            'tablist',
            'tabpanel',
            'term',
            'textbox',
            'timer',
            'toolbar',
            'tooltip',
            'tree',
            'treegrid',
            'treeitem',
          ];

          elementsWithRoles.forEach((el) => {
            const element = el as HTMLElement;
            const role = element.getAttribute('role');

            if (role && !validRoles.includes(role)) {
              invalidRoles.push(role);
            }
          });

          return {
            total: ariaPatterns.length,
            valid: ariaPatterns.filter((p) => p.isValid).length,
            patterns: ariaPatterns,
            commonIssues,
            invalidRoles,
            missingLabels,
          };
        },

        /**
         * Validate semantic elements
         */
        validateSemanticElements(includeHidden: boolean): unknown {
          const semanticElements = [
            'header',
            'nav',
            'main',
            'section',
            'article',
            'aside',
            'footer',
            'figure',
            'figcaption',
            'details',
            'summary',
            'mark',
            'time',
          ];

          const used: string[] = [];
          const missing: string[] = [];
          const misused: string[] = [];

          semanticElements.forEach((tag) => {
            const elements = document.querySelectorAll(tag);
            let hasVisible = false;

            elements.forEach((el) => {
              const element = el as HTMLElement;
              if (includeHidden || this.isVisible(element)) {
                hasVisible = true;

                // Check for misuse
                if (
                  tag === 'section' &&
                  !element.getAttribute('aria-label') &&
                  !element.getAttribute('aria-labelledby') &&
                  !element.querySelector('h1, h2, h3, h4, h5, h6')
                ) {
                  misused.push(tag);
                }
              }
            });

            if (hasVisible) {
              used.push(tag);
            }
          });

          // Check for missing essential elements
          if (!used.includes('main')) missing.push('main');
          if (!used.includes('header')) missing.push('header');
          if (!used.includes('footer')) missing.push('footer');

          return {
            total: used.length,
            used,
            missing,
            misused,
          };
        },
      };
    });
  }

  /**
   * Validate landmarks
   */
  private async validateLandmarks(page: Page, config: SemanticValidationConfig): Promise<unknown> {
    return await page.evaluate((includeHidden) => {
      return (window as any).semanticValidation.validateLandmarks(includeHidden);
    }, config.includeHiddenElements);
  }

  /**
   * Validate headings
   */
  private async validateHeadings(page: Page, config: SemanticValidationConfig): Promise<unknown> {
    return await page.evaluate((includeHidden) => {
      return (window as any).semanticValidation.validateHeadings(includeHidden);
    }, config.includeHiddenElements);
  }

  /**
   * Validate ARIA
   */
  private async validateAria(page: Page, config: SemanticValidationConfig): Promise<unknown> {
    return await page.evaluate((includeHidden) => {
      return (window as any).semanticValidation.validateAria(includeHidden);
    }, config.includeHiddenElements);
  }

  /**
   * Validate semantic elements
   */
  private async validateSemanticElements(
    page: Page,
    config: SemanticValidationConfig,
  ): Promise<unknown> {
    return await page.evaluate((includeHidden) => {
      return (window as any).semanticValidation.validateSemanticElements(includeHidden);
    }, config.includeHiddenElements);
  }

  /**
   * Generate comprehensive report
   */
  private generateReport(
    landmarks: any,
    headings: any,
    aria: any,
    semanticElements: any,
  ): SemanticValidationReport {
    // Calculate overall score
    let score = 100;

    // Landmark scoring
    if (landmarks.missing.length > 0) score -= landmarks.missing.length * 15;
    if (landmarks.duplicates.length > 0) score -= landmarks.duplicates.length * 10;
    score -= (landmarks.total - landmarks.valid) * 5;

    // Heading scoring
    if (!headings.hasH1) score -= 20;
    if (!headings.hasProperHierarchy) score -= 15;
    score -= headings.emptyHeadings * 5;

    // ARIA scoring
    score -= (aria.total - aria.valid) * 8;
    score -= aria.invalidRoles.length * 10;
    score -= aria.missingLabels.length * 5;

    // Semantic elements scoring
    score -= semanticElements.missing.length * 8;
    score -= semanticElements.misused.length * 10;

    score = Math.max(0, score);

    // Collect critical issues
    const criticalIssues: string[] = [];
    if (landmarks.missing.includes('main')) {
      criticalIssues.push('Missing main landmark');
    }
    if (!headings.hasH1) {
      criticalIssues.push('Missing H1 heading');
    }
    if (aria.invalidRoles.length > 0) {
      criticalIssues.push(`Invalid ARIA roles: ${aria.invalidRoles.join(', ')}`);
    }

    // Generate recommendations
    const recommendations: string[] = [];
    if (landmarks.missing.length > 0) {
      recommendations.push(`Add missing landmarks: ${landmarks.missing.join(', ')}`);
    }
    if (headings.skippedLevels.length > 0) {
      recommendations.push('Fix heading hierarchy - avoid skipping levels');
    }
    if (aria.missingLabels.length > 0) {
      recommendations.push('Add accessible labels to interactive elements');
    }
    if (semanticElements.missing.length > 0) {
      recommendations.push(
        `Consider using semantic elements: ${semanticElements.missing.join(', ')}`,
      );
    }

    return {
      landmarks,
      headings,
      aria,
      semanticElements,
      overallScore: Math.round(score),
      criticalIssues,
      recommendations,
    };
  }

  /**
   * Get empty analyses for disabled validations
   */
  private getEmptyLandmarkAnalysis(): {
    total: number;
    valid: number;
    missing: string[];
    duplicates: string[];
    analysis: string[];
  } {
    return { total: 0, valid: 0, missing: [], duplicates: [], analysis: [] };
  }

  private getEmptyHeadingAnalysis(): {
    total: number;
    structure: string[];
    hasH1: boolean;
    hasProperHierarchy: boolean;
    skippedLevels: string[];
    emptyHeadings: number;
  } {
    return {
      total: 0,
      structure: [],
      hasH1: true,
      hasProperHierarchy: true,
      skippedLevels: [],
      emptyHeadings: 0,
    };
  }

  private getEmptyAriaAnalysis(): {
    total: number;
    valid: number;
    patterns: string[];
    commonIssues: string[];
    invalidRoles: string[];
    missingLabels: string[];
  } {
    return {
      total: 0,
      valid: 0,
      patterns: [],
      commonIssues: [],
      invalidRoles: [],
      missingLabels: [],
    };
  }

  private getEmptySemanticAnalysis(): {
    total: number;
    used: string[];
    missing: string[];
    misused: string[];
  } {
    return { total: 0, used: [], missing: [], misused: [] };
  }
}

export default SemanticValidator;
